# Budget Import Implementation Checklist

## Overview

Implementation plan for Excel budget import feature with CostX export support. Users upload .xlsx files, system processes and classifies data, user reviews and confirms, then data is imported to database.

## 1 · Remove old plan schema

- [x] Remove obsolete file‑upload field from `schema.ts`
- [x] Add Zod schema `ImportDataSchema` with `project_id` and `items[]`:

  ```ts
  import { z } from 'zod';

  /**
   * One budget line item extracted from the CostX sheet
   * and ready for insertion.
   */
  export const ImportLineItemSchema = z.object({
  	// Foreign-key lookup for wbs_library_item; we
  	// pass the CostX code string so the backend can
  	// create/lookup the WBS item in one transaction.
  	code: z.string().min(1),

  	// Full description after we’ve inserted any
  	// [Category] prefixes.
  	description: z.string().min(1),

  	quantity: z.number().nonnegative(),
  	unit: z.string().min(1),

  	// “Rate” in the CostX file maps to material_rate.
  	material_rate: z.number().nonnegative(),

  	// Optional cells
  	factor: z.number().nonnegative().optional(),

  	// You can include optional fields we might expose in
  	// the UI later (labor_rate, productivity_per_hour, remarks…)
  	labor_rate: z.number().nonnegative().optional(),
  	productivity_per_hour: z.number().nonnegative().optional(),
  	remarks: z.string().optional(),
  });

  /**
   * Overall payload sent to the RPC.
   */
  export const ImportDataSchema = z.object({
  	// Context for the server-side insert
  	project_id: z.string().uuid(),

  	// Rows the user has confirmed for import
  	items: z.array(ImportLineItemSchema).min(1, 'At least one line item must be provided'),
  });
  ```

## 🛢️ 2 · Database & Backend

### 2.1 Postgres RPC

- [ ] Create SQL function `import_budget_data(project_id uuid, items jsonb)` matching `ImportDataSchema` _(Started - migration file created but not yet applied)_
- [ ] Wrap inserts/updates in `BEGIN … EXCEPTION … ROLLBACK`
- [ ] Accept array objects `{ wbs_code, cost_scope, quantity, unit, material_rate, factor }` matching `ImportLineItemSchema`
- [ ] Ensure that the items are set with a 'Custom' item_type and that the client_id and project_id are correctly set.
- [ ] Upsert WBS hierarchy → return `wbs_code → id` map
- [ ] Bulk‑insert into `budget_line_item_current` referencing map
- [ ] Raise `duplicate_code` error if `wbs_code` already exists for project
- [ ] Return JSON `{ inserted_count, wbs_created_count, duration_ms }`

### 2.2 Permissions & Security

- [ ] Ensure RPC obeys existing RLS policies
- [ ] Expose via typed Supabase‑JS client `supabase.rpc('import_budget_data', …)`

## 🧰 3 · Shared Utilities & Types

- [x] Create `src/lib/budget_import_utils.ts` with:

  ```ts
  export type ExcelRow = Record<string, string | number | null>;
  export type ProcessedRow = {
  	originalIndex: number;
  	code?: string;
  	description?: string;
  	quantity?: number;
  	uom?: string;
  	rate?: number;
  	subtotal?: number;
  	factor?: number;
  	hasYellowFill?: boolean;
  };
  export type ClassifiedRow = ProcessedRow & {
  	classification: 'detail' | 'summary' | 'category' | 'ignore';
  	categoryPrefix?: string;
  	finalDescription?: string;
  	appliedFactor?: number;
  };
  export type ColumnMapping = {
  	code?: number;
  	description?: number;
  	quantity?: number;
  	uom?: number;
  	rate?: number;
  	subtotal?: number;
  	factor?: number;
  };
  ```

- [x] **Add `COLUMN_PATTERNS` constant for header auto‑detection**

  ```ts
  /**
   * First RegExp to match a header wins (case‑insensitive).
   * List patterns most‑specific → least‑specific.
   */
  export const COLUMN_PATTERNS: Record<keyof ColumnMapping, RegExp[]> = {
  	code: [/^code$/i, /^wbs\.?code$/i, /\bwbs\b/i, /\bcost\s*code\b/i],
  	description: [/^description$/i, /^desc$/i, /\b(scope|item\s*name)\b/i],
  	quantity: [/^quantity$/i, /^qty$/i, /\bq(?:uantity)?ty?\b/i],
  	uom: [/^uom$/i, /^unit(?:\s*of\s*measure)?$/i, /^units?$/i],
  	rate: [/^rate$/i, /\b(material)?\s*rate\b/i, /^unit\s*rate$/i],
  	subtotal: [/^sub\s*total$/i, /^subtotal$/i, /\bsub[-\s]*total\b/i],
  	factor: [/^factor$/i, /\bmultiplier\b/i, /\badjust(?:ment)?\s*factor\b/i],
  };
  ```

- [x] Export helper `matchColumns(headers: string[]): ColumnMapping`
- [ ] Unit tests for each regex list using representative header strings

## 📥 4 · Excel Parsing & Data Processing

### 4.1 Excel Parser (`ExcelParser.ts`)

- [x] Use existing `xlsx` library _(Added xlsx dependency to package.json)_

  - [ ] `````typescript
                import * as XLSX from 'xlsx';
                ```
            ````
        `````

  // Read with cell styles for yellow fill detection
  const workbook = XLSX.read(buffer, {
  type: 'buffer',
  cellStyles: true,
  cellDates: true,
  });

  ```

  ```

- [x] Read **first** worksheet via `xlsx.read(data, { cellStyles: true })`
- [x] Detect header row among first 10 rows (most filled cells)
- [x] Collect `hasYellowFill` flags (look for `style.fill.fgColor.rgb === 'FFFFFF00'`)
- [x] Return `{ rows: ProcessedRow[], hasMultipleSheets, hasYellowFill }`

### 4.2 Column Classifier (`ColumnClassifier.svelte`)

- [ ] Auto‑map headers using `COLUMN_PATTERNS`
- [ ] Allow dropdown manual override per column
- [ ] Sample values rows 1‑5 under header
- [ ] Required columns validation: Code, Description, Quantity, Rate
- [ ] Optional columns: UOM, SubTotal, Factor
- [ ] UI shows dropdown for each detected column with options: Code, Description, Quantity, UOM, Rate, SubTotal, Factor, Ignore
- [ ] Highlight conflicts (same target selected twice)
- [ ] Block ‘Next’ until required columns (Code, Description, Quantity, Rate) uniquely mapped

### 4.3 Row Classifier (`RowClassifier.svelte`)

- [ ] Auto classify rows:

  - **detail** – has Code
  - **category** – empty Code, non‑empty Description, usually surrounded by ≥1 empty row
  - **summary** – Description starts with `TOTAL `
  - **ignore** – headers/empty/other

- [ ] Maintain **`categoryStack: string[]`** while scanning rows:

  1. On encountering a **category** row → _push_ its cleaned description (`trim()`) onto stack.
  2. On leaving category block (next empty row) → _pop_ the last entry.
  3. Stack depth = nesting level.

- [ ] For each **detail** row, build `categoryPrefix` by concatenating stack values **outer‑to‑inner**, each wrapped in square brackets, e.g.:

  ```md
  [Level‑1][Phase A] Original description
  ```

  **Rules:**

  - No whitespace inside the brackets.
  - Preserve original description capitalization/spaces.
  - Join prefixes with no separator other than the closing/opening brackets.

- [ ] Store result in `finalDescription` column so later steps never recalculate.
- [ ] If the user edits a category name in UI ➜ **re‑compute prefixes live** for every affected detail row.
- [ ] Factor application logic:
  ```typescript
  // For SUMMARY rows with Factor:
  // 1. Extract base description after "TOTAL " prefix
  // 2. Find matching detail row(s) by code hierarchy
  // 3. Apply factor to those detail row(s)
  // Example: "TOTAL Frame" matches code "*******"
  ```
- [ ] UI specifics:

  - **Preview Column**: show side‑by‑side _Original_ vs _Prefixed_ descriptions with diff‑style highlight.
  - **Nested indicator**: indent category rows proportionally (`pl‑{level*4}`).
  - **Breadcrumb chip** in header area summarises current stack (`Level‑1 › Phase A › …`).

- [ ] Propagate summary row **factor** to its detail rows by matching text suffix
- [ ] UI:
  - [ ] **First Column** ⇒ dropdown (`detail | summary | category | ignore`).
  - [ ] **Editing a category row** opens inline text field _and_ immediately recalculates prefixes for its children; show toast `“Updated 12 child rows”`.
  - [ ] **Prefix preview column**: display new `finalDescription` in bold; strike‑through old description if changed.
  - [ ] **Nested badges**: For nested stacks render `[Outer][Inner]` chips with tailwind `bg‑slate‑200 text‑xs rounded`.
  - [ ] Grey out `summary` & `ignore` rows.
  - [ ] Factor mapping UI unchanged but **adds badge** on detail rows after prefix chips.
  - [ ] Warn if two sibling categories share identical name (could cause ambiguous prefixes).

### 4.4 Data Transformer (`DataTransformer.ts`)

- [ ] Assert every exported `detail` row’s `finalDescription` **starts with one or more `[.*]` prefixes OR none if no category stack**.
- [ ] Strip double‑spaces created by user edits.
- [ ] Unit test: given nested categories _Civil_ > _Plan 1_ the description becomes `[Civil][Plan 1] …` exactly.
- [ ] Filter to `detail` rows only
- [ ] Apply category prefixes and factors to descriptions/rates
- [ ] Validate values (positive qty/rate, ≤2 dp etc.)
- [ ] Apply factors from matched summary rows
- [ ] Validate data types and ranges
- [ ] Build `items[]` payload for RPC

### 4.5 WBS Hierarchy Builder (`wbsBuilder.ts`)

- [x] Parse code e.g. `*******` → `{ level, in_level_code, parent_code }` _(Implemented in budget_import_utils.ts)_
- [ ] Ensure ancestor items created first; deduplicate shared parents _(Partially implemented in RPC function)_

## 💻 5 · Front‑End UI Flow

Route: `src/routes/org/[org_name]/clients/[client_name]/projects/[project_name]/budget/import/+page.svelte` _(Basic structure exists)_

### 5.1 Step 1 – File Drop

- [x] Use `FileDropZone` with `accept=".xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"` _(Basic implementation exists)_
- [ ] Provide link to CostX export instructions
- [ ] Persist file buffer & meta to `localStorage.import_state`

### 5.2 Step 2 – Raw Preview

- [x] Show first 20 rows unedited and allow paginating _(Basic table preview implemented)_
- [ ] Alerts:

  - Multiple sheets → warn only first is read
  - Yellow SubTotal cells → suggest deeper export levels
  - Missing expected columns

### 5.3 Step 3 – Column Mapping

- [ ] Render `ColumnClassifier.svelte`
- [ ] Confirm/override column detection
- [ ] Persist mapping to `localStorage.import_state.columnMap`

### 5.4 Step 4 – Row Classification

- [ ] Render `RowClassifier.svelte`
- [ ] Allow edits; live factor/description preview
- [ ] Persist classified rows to `localStorage.import_state.rows`

### 5.5 Step 5 – Import Summary

- [ ] Statistics display:
  - [ ] Count of detail rows to import: `rows.length`
  - [ ] Total budget amount: sum(`quantity * material_rate * (factor ?? 1)`)
- [ ] Show table of WBS levels to be created
- [ ] Validation warnings for unusual data (negative values, zero rates, zero quantities, etc.)
- [ ] Disable ‘Import’ button if validation errors exist

### 5.6 – Submit

- [ ] Call RPC `import_budget_data`
- [ ] Show loading spinner + disable button
- [ ] On success: toast ✅ + redirect to `/budget`
- [ ] On failure: show error + option to return to Step 4

### 5.7 – Persistence & Navigation

- [ ] Use `searchParams` ?step=N to track wizard progress (no nested routes)
- [ ] Clear `localStorage.import_state` when import completes or user cancels

## ✅ 6 · Testing & Quality Assurance

### 6.1 Unit Tests (Vitest)

- [ ] `ExcelParser` handles header row detection & yellow fill flag
- [ ] `matchColumns` maps headers correctly given varied synonyms
- [ ] `RowClassifier`
  - [ ] correctly identifies classification cases
  - [ ] `buildPrefixes()` produces `[Outer][Inner]` order.
  - [ ] Editing category name in singleton row updates `finalDescription` of children.
  - [ ] Deleting a category row clears its prefix on children.
- [ ] `DataTransformer`
  - [ ] output matches snapshot for sample file
  - [ ] Assert RPC payload `cost_scope` contains full prefixed description.
  - [ ] Snapshot test sample nested category file.

### 6.2 Component Tests (vitest-browser-svelte)

- [ ] ColumnClassifier dropdown overrides emit mapping events
- [ ] RowClassifier category renaming updates prefixes live

### 6.3 E2E Tests (Playwright)

- [ ] Happy‑path import flows end‑to‑end and shows success toast
- [ ] Error path: duplicate codes triggers RPC error & UI display

## 📚 7 · Storybook

- [ ] Stories for `ColumnClassifier`, `RowClassifier`, import wizard steps 1–5
- [ ] Add controls for sample data injection

## 📝 8 · Docs & PR

- [ ] Update `README.md` feature list
- [ ] Add `/docs/budget-import.md` with screenshots & tech notes

---

## 🚧 Implementation Status

### ✅ Completed

- **Schema Updates**: Updated `schema.ts` with `ImportLineItemSchema` and `ImportDataSchema`
- **Core Utilities**: Created `budget_import_utils.ts` with types, column patterns, and helper functions
- **Excel Parser**: Created `excel_parser.ts` with full Excel parsing functionality
- **Dependencies**: Added `xlsx` library to package.json

### 🔄 In Progress

- **Database RPC**: Started migration file for `import_budget_data` function (needs completion and testing)

### ⏳ Next Steps

1. **Complete Database RPC Function**: Finish and test the `import_budget_data` SQL function
2. **Create UI Components**: Build `ColumnClassifier.svelte` and `RowClassifier.svelte` components
3. **Update Import Page**: Implement the multi-step wizard UI flow
4. **Add Tests**: Create unit tests for utilities and components
5. **E2E Testing**: Test the complete import workflow

### 📋 Additional Tasks Identified

- [ ] Add validation for WBS code format and hierarchy consistency
- [ ] Implement better error handling for malformed Excel files
- [ ] Add progress indicators for large file processing
- [ ] Create sample Excel template for users
- [ ] Add data preview with pagination for large datasets

---

✅ When every box above is ticked, the import flow will correctly detect categories, prefix detail rows with `[Category]` strings (including nested stacks), and write those prefixed descriptions to the database via the RPC.
